# HRFlow Webhook Integration

This document explains how to set up and use the HRFlow webhook integration for automatic CV processing.

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```bash
# HRFlow Configuration
HRFLOW_API_KEY=your_hrflow_api_key_here
HRFLOW_API_URL=https://api.hrflow.ai/v1
HRFLOW_SOURCE_KEY=your_hrflow_source_key_here
HRFLOW_WEBHOOK_SECRET=your_webhook_secret_here
```

### 2. Database Migration

Run the database migration to create the webhook tables:

```bash
alembic upgrade head
```

### 3. Configure HRFlow Webhook

In your HRFlow dashboard, configure the webhook URL to point to:
```
https://your-domain.com/api/v1/webhooks/hrflow
```

## Usage

### 1. Upload CV to HRFlow

When uploading a CV file, use the updated `parse_cv` method:

```python
from app.services.hrflow_service import HRFlowService

hrflow_service = HRFlowService()

# Upload CV with webhook support
result = await hrflow_service.parse_cv(
    file_content=cv_file_bytes,
    filename="resume.pdf"
    # Source key is automatically taken from environment configuration
)

# For async processing (webhook enabled), you'll get:
{
    "status": "processing",
    "message": "Your profile has been successfully sent to the Parsing Queue...",
    "webhook_enabled": True
}
```

### 2. Webhook Processing Flow

1. **CV Upload**: CV is uploaded to HRFlow via API
2. **Queue Processing**: HRFlow processes the CV asynchronously
3. **Webhook Notification**: HRFlow sends webhook notification when processing is complete
4. **Database Update**: Your application receives the webhook and updates the candidate record

### 3. Webhook Payload Example

HRFlow sends webhooks as form-encoded data:

```
POST /api/v1/webhooks/hrflow
Content-Type: application/x-www-form-urlencoded

type=profile.parsing.success&origin=api&message=profile+parsing+succeed&profile=%7B%22key%22%3A+%22d821393853fc32b08c93b8d38590817c72048ec4%22%2C+%22source%22%3A+%7B%22key%22%3A+%22d900ec70c67d43c71027f9bc63ec3b5b3e16c1d8%22%7D%7D
```

Decoded form data:
- `type`: `profile.parsing.success`
- `origin`: `api`
- `message`: `profile parsing succeed`
- `profile`: `{"key": "d821393853fc32b08c93b8d38590817c72048ec4", "source": {"key": "d900ec70c67d43c71027f9bc63ec3b5b3e16c1d8"}}`

### 4. Candidate Model Updates

The candidate model now includes:
- `hrflow_source_key`: HRFlow source key for tracking
- Enhanced webhook processing support
- Automatic profile data updates via webhooks

### 5. Webhook Security

Webhooks are secured using HMAC-SHA256 signatures. The webhook service validates the signature using your `HRFLOW_WEBHOOK_SECRET`.

## API Endpoints

### Webhook Endpoint
- **POST** `/api/v1/webhooks/hrflow` - Receives HRFlow webhook notifications

### Test Endpoint
- **GET** `/api/v1/webhooks/test` - Test webhook configuration

## Monitoring

All webhook events are logged in the `webhook_events` table with:
- Event type and origin
- Processing status
- Error messages (if any)
- Timestamps for debugging

## Error Handling

The webhook system includes comprehensive error handling:
- Invalid signature verification
- Malformed payload handling
- Database transaction rollback on errors
- Detailed error logging

## Example Integration

```python
# In your candidate upload handler
async def upload_candidate_cv(file: UploadFile, project_id: int, db: Session):
    # Save candidate record with initial status
    candidate = Candidate(
        project_id=project_id,
        cv_filename=file.filename,
        processing_status="pending"
    )
    db.add(candidate)
    db.commit()
    
    # Upload to HRFlow with webhook
    hrflow_service = HRFlowService()
    result = await hrflow_service.parse_cv(
        file_content=await file.read(),
        filename=file.filename
    )
    
    if result.get("status") == "processing":
        # Webhook will update the candidate when processing is complete
        candidate.processing_status = "processing"
        candidate.hrflow_profile_id = result.get("profile_key")  # If available
        db.commit()
    
    return candidate
```
