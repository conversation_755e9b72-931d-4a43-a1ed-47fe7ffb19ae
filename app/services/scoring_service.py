import json
import openai
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import HTTPException, status

from app.core.config import settings
from app.services.security_service import SecurityService


class ScoringService:
    def __init__(self):
        self.security_service = SecurityService()
        openai.api_key = settings.OPENAI_API_KEY

    async def score_candidate(
        self, 
        candidate_data: Dict[str, Any], 
        job_requirements: Dict[str, Any],
        scoring_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Score a candidate against job requirements using AI
        """
        try:
            # Validate and sanitize inputs
            sanitized_candidate = self.security_service.sanitize_candidate_data(candidate_data)
            
            # Create structured prompt
            prompt = self._create_scoring_prompt(sanitized_candidate, job_requirements, scoring_config)
            
            # Call OpenAI API
            response = await self._call_openai_api(prompt)
            
            # Parse and validate response
            scoring_results = self._parse_scoring_response(response)
            
            # Calculate final score
            final_score = self._calculate_final_score(scoring_results, scoring_config)
            
            return {
                "scores": scoring_results,
                "final_score": final_score,
                "recommendation": self._generate_recommendation(final_score),
                "confidence_level": self._assess_confidence(scoring_results),
                "flags": self._identify_flags(scoring_results),
                "scored_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            # Fallback scoring if AI fails
            return self._fallback_scoring(candidate_data, job_requirements)

    def _create_scoring_prompt(
        self, 
        candidate_data: Dict[str, Any], 
        job_requirements: Dict[str, Any],
        scoring_config: Dict[str, Any]
    ) -> str:
        """
        Create a structured prompt for AI scoring
        """
        prompt = f"""
You are an expert HR professional tasked with scoring a candidate's CV against specific job requirements.

IMPORTANT INSTRUCTIONS:
- Respond ONLY with valid JSON
- Score each dimension from 0-100
- Provide clear, specific reasoning for each score
- Be objective and fair in your assessment

JOB REQUIREMENTS:
{json.dumps(job_requirements, indent=2)}

CANDIDATE DATA:
{json.dumps(candidate_data, indent=2)}

SCORING DIMENSIONS:
1. education_relevance: How well does the candidate's education match the job requirements?
2. skills_match: How well do the candidate's skills align with required and preferred skills?
3. experience_quality: Quality and relevance of work experience
4. technical_proficiency: Technical skills and expertise level
5. career_progression: Career growth and advancement pattern
6. language_fit: Language skills matching requirements

Respond with this exact JSON structure:
{{
    "education_relevance": {{
        "score": <0-100>,
        "reasoning": "<specific explanation>"
    }},
    "skills_match": {{
        "score": <0-100>,
        "reasoning": "<specific explanation>"
    }},
    "experience_quality": {{
        "score": <0-100>,
        "reasoning": "<specific explanation>"
    }},
    "technical_proficiency": {{
        "score": <0-100>,
        "reasoning": "<specific explanation>"
    }},
    "career_progression": {{
        "score": <0-100>,
        "reasoning": "<specific explanation>"
    }},
    "language_fit": {{
        "score": <0-100>,
        "reasoning": "<specific explanation>"
    }}
}}
"""
        return prompt

    async def _call_openai_api(self, prompt: str) -> str:
        """
        Call OpenAI API with error handling
        """
        try:
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert HR professional. Respond only with valid JSON."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=2000,
                temperature=0.3,
                timeout=30
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI scoring service error: {str(e)}"
            )

    def _parse_scoring_response(self, response: str) -> Dict[str, Dict[str, Any]]:
        """
        Parse and validate AI response
        """
        try:
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response[json_start:json_end]
            scoring_data = json.loads(json_str)
            
            # Validate structure
            required_dimensions = [
                "education_relevance", "skills_match", "experience_quality",
                "technical_proficiency", "career_progression", "language_fit"
            ]
            
            for dimension in required_dimensions:
                if dimension not in scoring_data:
                    raise ValueError(f"Missing dimension: {dimension}")
                
                if "score" not in scoring_data[dimension] or "reasoning" not in scoring_data[dimension]:
                    raise ValueError(f"Invalid structure for dimension: {dimension}")
                
                # Validate score range
                score = scoring_data[dimension]["score"]
                if not isinstance(score, (int, float)) or score < 0 or score > 100:
                    raise ValueError(f"Invalid score for {dimension}: {score}")
            
            return scoring_data
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error parsing AI response: {str(e)}"
            )

    def _calculate_final_score(
        self, 
        scoring_results: Dict[str, Dict[str, Any]], 
        scoring_config: Dict[str, Any]
    ) -> float:
        """
        Calculate weighted final score
        """
        weights = scoring_config.get("weights", {
            "education_relevance": 0.25,
            "skills_match": 0.25,
            "experience_quality": 0.20,
            "technical_proficiency": 0.15,
            "career_progression": 0.10,
            "language_fit": 0.05
        })
        
        final_score = 0.0
        for dimension, weight in weights.items():
            if dimension in scoring_results:
                final_score += scoring_results[dimension]["score"] * weight
        
        return round(final_score, 2)

    def _generate_recommendation(self, final_score: float) -> str:
        """
        Generate recommendation based on final score
        """
        if final_score >= 85:
            return "Highly Recommended - Excellent match for the position"
        elif final_score >= 70:
            return "Recommended - Good match with strong potential"
        elif final_score >= 55:
            return "Consider - Moderate match, may need additional evaluation"
        elif final_score >= 40:
            return "Weak Match - Significant gaps in requirements"
        else:
            return "Not Recommended - Poor match for the position"

    def _assess_confidence(self, scoring_results: Dict[str, Dict[str, Any]]) -> str:
        """
        Assess confidence level based on score consistency
        """
        scores = [result["score"] for result in scoring_results.values()]
        
        # Calculate standard deviation
        mean_score = sum(scores) / len(scores)
        variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)
        std_dev = variance ** 0.5
        
        if std_dev <= 10:
            return "High"
        elif std_dev <= 20:
            return "Medium"
        else:
            return "Low"

    def _identify_flags(self, scoring_results: Dict[str, Dict[str, Any]]) -> List[str]:
        """
        Identify potential flags or concerns
        """
        flags = []
        
        for dimension, result in scoring_results.items():
            score = result["score"]
            if score < 30:
                flags.append(f"Low {dimension.replace('_', ' ')}")
        
        return flags

    def _fallback_scoring(
        self, 
        candidate_data: Dict[str, Any], 
        job_requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Fallback scoring when AI is unavailable
        """
        # Simple rule-based scoring
        scores = {
            "education_relevance": {"score": 50, "reasoning": "Fallback scoring - AI unavailable"},
            "skills_match": {"score": 50, "reasoning": "Fallback scoring - AI unavailable"},
            "experience_quality": {"score": 50, "reasoning": "Fallback scoring - AI unavailable"},
            "technical_proficiency": {"score": 50, "reasoning": "Fallback scoring - AI unavailable"},
            "career_progression": {"score": 50, "reasoning": "Fallback scoring - AI unavailable"},
            "language_fit": {"score": 50, "reasoning": "Fallback scoring - AI unavailable"}
        }
        
        return {
            "scores": scores,
            "final_score": 50.0,
            "recommendation": "Manual Review Required - AI scoring unavailable",
            "confidence_level": "Low",
            "flags": ["AI_SCORING_FAILED"],
            "scored_at": datetime.utcnow().isoformat()
        }
