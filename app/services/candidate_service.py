from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from fastapi import HTTPException, status, UploadFile
from datetime import datetime

from app.models.candidate import Candidate
from app.models.project import Project
from app.schemas.candidate import CandidateUpdate, CandidateList, ParsedData
from app.services.file_service import FileService
from app.services.hrflow_service import HRFlowService
from app.services.security_service import SecurityService
from app.services.scoring_service import ScoringService
from app.constants.candidate_status import CandidateStatus


class CandidateService:
    def __init__(self, db: Session):
        self.db = db
        self.file_service = FileService()
        self.hrflow_service = HRFlowService()
        self.security_service = SecurityService()
        self.scoring_service = ScoringService()

    def get_candidate(self, candidate_id: int, owner_id: int) -> Optional[Candidate]:
        """Get candidate by ID, ensuring owner has access"""
        return self.db.query(Candidate).join(Project).filter(
            Candidate.id == candidate_id,
            Project.owner_id == owner_id
        ).first()

    def get_project_candidates(
        self, 
        project_id: int, 
        owner_id: int, 
        sort_by: str = "final_score",
        order: str = "desc",
        selected_only: bool = False
    ) -> List[CandidateList]:
        """Get candidates for a project with sorting"""
        # Verify project ownership
        project = self.db.query(Project).filter(
            Project.id == project_id,
            Project.owner_id == owner_id
        ).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        query = self.db.query(Candidate).filter(Candidate.project_id == project_id)
        
        if selected_only:
            query = query.filter(Candidate.selected == True)

        # Apply sorting
        if sort_by == "final_score":
            if order == "desc":
                query = query.order_by(desc(Candidate.final_score))
            else:
                query = query.order_by(asc(Candidate.final_score))
        elif sort_by == "upload_date":
            if order == "desc":
                query = query.order_by(desc(Candidate.uploaded_at))
            else:
                query = query.order_by(asc(Candidate.uploaded_at))
        elif sort_by == "name":
            # This would require extracting name from parsed_data
            pass

        candidates = query.all()
        
        # Convert to list format
        candidate_list = []
        for candidate in candidates:
            personal_info = None
            if candidate.parsed_data:
                personal_info = candidate.parsed_data.get("personal_info")
            
            candidate_list.append(CandidateList(
                id=candidate.id,
                original_filename=candidate.original_filename,
                final_score=candidate.final_score,
                confidence_level=candidate.confidence_level,
                selected=candidate.selected,
                processing_status=candidate.processing_status,
                uploaded_at=candidate.uploaded_at,
                personal_info=personal_info
            ))
        
        return candidate_list

    def get_processing_status(self, candidate_ids: List[int], owner_id: int) -> Dict[str, Any]:
        """Get processing status for specific candidates"""
        candidates = self.db.query(Candidate).join(Project).filter(
            Candidate.id.in_(candidate_ids),
            Project.owner_id == owner_id
        ).all()
        
        status_info = []
        for candidate in candidates:
            status_info.append({
                "candidate_id": candidate.id,
                "filename": candidate.original_filename,
                "processing_status": candidate.processing_status,
                "error_message": candidate.error_message,
                "final_score": candidate.final_score,
                "uploaded_at": candidate.uploaded_at,
                "processed_at": candidate.processed_at,
                "scored_at": candidate.scored_at
            })
        
        return {
            "candidates": status_info,
            "total_count": len(status_info)
        }

    async def upload_cvs(
        self, 
        project_id: int, 
        files: List[UploadFile], 
        owner_id: int, 
        auto_score: bool = True
    ) -> Dict[str, Any]:
        """Upload and process CVs with improved error handling and transaction management"""
        # Input validation
        if not files:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No files provided"
            )
        
        if len(files) > 50:  # Reasonable upper limit
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Too many files. Maximum 50 files allowed per upload."
            )
        
        # Verify project ownership
        project = self.db.query(Project).filter(
            Project.id == project_id,
            Project.owner_id == owner_id
        ).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        # Process files
        try:
            processed_files = await self.file_service.process_multiple_files(files)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File processing failed: {str(e)}"
            )
        
        candidates = []
        processing_jobs = []
        successful_uploads = 0
        failed_uploads = 0
        
        try:
            for original_filename, temp_path, file_content in processed_files:
                candidate = None
                try:
                    # Validate file content size
                    if len(file_content) == 0:
                        raise Exception("Empty file content")
                    
                    # Create candidate record
                    candidate = Candidate(
                        project_id=project_id,
                        cv_filename=temp_path.split('/')[-1],
                        original_filename=original_filename,
                        file_size=len(file_content),
                        processing_status=CandidateStatus.PROCESSING
                    )
                    
                    self.db.add(candidate)
                    self.db.flush()  # Get the ID without committing
                    
                    processing_jobs.append({
                        "candidate_id": candidate.id,
                        "filename": original_filename,
                        "status": "processing"
                    })
                    
                    # Process CV with HRFlow
                    await self._process_candidate_cv(candidate, file_content, auto_score)
                    
                    # Update processing job status
                    for job in processing_jobs:
                        if job["candidate_id"] == candidate.id:
                            job["status"] = "completed"
                            break
                    
                    candidates.append(candidate)
                    successful_uploads += 1
                    
                    # Commit individual candidate to avoid losing work
                    self.db.commit()
                    
                except Exception as e:
                    # Rollback the current transaction for this candidate
                    self.db.rollback()

                    # Get detailed error information
                    error_message = str(e) if str(e) else f"{type(e).__name__}: {repr(e)}"

                    # Also get traceback for debugging
                    import traceback
                    error_traceback = traceback.format_exc()

                    print(f"Error processing candidate {original_filename}: {error_message}")
                    print(f"Full traceback: {error_traceback}")

                    if candidate and candidate.id:
                        # If candidate was created, update its status
                        try:
                            candidate.processing_status = "failed"
                            candidate.error_message = error_message
                            self.db.add(candidate)
                            self.db.commit()

                            # Update processing job status
                            for job in processing_jobs:
                                if job["candidate_id"] == candidate.id:
                                    job["status"] = "failed"
                                    job["error"] = error_message
                                    break

                            candidates.append(candidate)
                        except Exception as db_error:
                            print(f"Failed to update candidate error status: {str(db_error)}")

                    failed_uploads += 1
                    
        finally:
            # Cleanup temporary files
            for _, temp_path, _ in processed_files:
                self.file_service.cleanup_file(temp_path)
        
        return {
            "success": True,
            "data": {
                "uploaded_count": len(candidates),
                "successful_uploads": successful_uploads,
                "failed_uploads": failed_uploads,
                "processing_jobs": processing_jobs
            }
        }

    async def _process_candidate_cv(self, candidate: Candidate, file_content: bytes, auto_score: bool):
        """Process a single candidate CV with enhanced error handling and webhook support"""
        try:
            # Parse CV with HRFlow (may return async processing status)
            hrflow_response = await self.hrflow_service.parse_cv(
                file_content, 
                candidate.original_filename
            )
            
            if not hrflow_response:
                raise Exception("Invalid or empty response from CV parsing service")

            print(f"HRFlow response status: {hrflow_response.get('status')}")
            print(f"HRFlow response: {hrflow_response}")

            # Check processing mode
            if hrflow_response.get("status") == "processing":
                # Async processing - webhook will complete the processing later
                print("Async processing mode - webhook will update candidate later")
                candidate.processing_status = CandidateStatus.PROCESSING
                candidate.hrflow_response = hrflow_response
                # The webhook will complete the processing later
                return

            elif hrflow_response.get("status") == "completed":
                # Sync processing - we have the data immediately
                print("Sync processing mode - extracting data immediately")

                # Set the profile ID from the response
                profile_key = hrflow_response.get("profile_key")
                if profile_key:
                    candidate.hrflow_profile_id = profile_key
                    print(f"Set hrflow_profile_id to: {profile_key}")

                # Extract structured data
                parsed_data = self.hrflow_service.extract_structured_data(hrflow_response)

                if not parsed_data:
                    raise Exception("Failed to extract structured data from CV")

                # Sanitize data for security
                sanitized_data = self.security_service.sanitize_candidate_data(parsed_data)

                # Update candidate record
                candidate.hrflow_response = hrflow_response
                candidate.parsed_data = sanitized_data
                candidate.processing_status = CandidateStatus.PARSING_COMPLETED
                candidate.processed_at = datetime.utcnow()

                print(f"Candidate {candidate.id} processed successfully with sync parsing")

                # Continue to scoring if enabled
                # Don't return here, let the method continue to scoring
            else:
                # Legacy handling for backward compatibility
                print("Legacy processing mode - attempting to extract data")

                if not hrflow_response.get("data"):
                    raise Exception("Invalid or empty response from CV parsing service")

                # Extract structured data
                parsed_data = self.hrflow_service.extract_structured_data(hrflow_response)

                if not parsed_data:
                    raise Exception("Failed to extract structured data from CV")

                # Sanitize data for security
                sanitized_data = self.security_service.sanitize_candidate_data(parsed_data)

                # Update candidate record
                candidate.hrflow_profile_id = hrflow_response.get("data", {}).get("id")
                candidate.hrflow_response = hrflow_response
                candidate.parsed_data = sanitized_data
                candidate.processing_status = CandidateStatus.PARSING_COMPLETED
                candidate.processed_at = datetime.utcnow()
            
            # Don't commit here - let the calling method handle transactions
            
            # Score candidate if auto_score is enabled
            if auto_score:
                await self._score_candidate(candidate)
                
        except Exception as e:
            candidate.processing_status = CandidateStatus.FAILED
            candidate.error_message = str(e)
            candidate.processed_at = datetime.utcnow()
            # Don't commit here - let the calling method handle transactions
            raise e

    async def _score_candidate(self, candidate: Candidate):
        """Score a candidate against job requirements with improved error handling"""
        try:
            # Get project and job requirements
            project = self.db.query(Project).filter(Project.id == candidate.project_id).first()
            if not project:
                raise Exception("Project not found for scoring")
            
            if not project.job_requirements:
                raise Exception("Job requirements not configured for this project")

            # Validate candidate data exists
            if not candidate.parsed_data:
                raise Exception("No parsed candidate data available for scoring")

            # Score the candidate
            scoring_results = await self.scoring_service.score_candidate(
                candidate.parsed_data,
                project.job_requirements,
                project.scoring_config or {}
            )

            if not scoring_results or "final_score" not in scoring_results:
                raise Exception("Invalid scoring results received")

            # Update candidate with scoring results
            candidate.scoring_results = scoring_results
            candidate.final_score = scoring_results["final_score"]
            candidate.confidence_level = scoring_results.get("confidence_level", "Low")
            candidate.scored_at = datetime.utcnow()
            candidate.processing_status = CandidateStatus.COMPLETED

            # Don't commit here - let the calling method handle transactions

        except Exception as e:
            print(f"Error scoring candidate {candidate.id}: {str(e)}")
            # Set fallback score with more detailed error info
            candidate.final_score = 0.0
            candidate.confidence_level = "Low"
            candidate.scored_at = datetime.utcnow()
            candidate.scoring_results = {
                "error": str(e),
                "fallback_applied": True,
                "final_score": 0.0,
                "confidence_level": "Low"
            }
            # Don't commit here - let the calling method handle transactions

    def update_candidate_selection(
        self, 
        candidate_id: int, 
        selection_data: CandidateUpdate, 
        owner_id: int
    ) -> Optional[Candidate]:
        """Update candidate selection status"""
        candidate = self.get_candidate(candidate_id, owner_id)
        if not candidate:
            return None

        if selection_data.selected is not None:
            candidate.selected = selection_data.selected
        
        if selection_data.selection_notes is not None:
            candidate.selection_notes = selection_data.selection_notes

        self.db.commit()
        self.db.refresh(candidate)
        return candidate

    def bulk_select_candidates(
        self, 
        candidate_ids: List[int], 
        selected: bool, 
        owner_id: int
    ) -> Dict[str, Any]:
        """Bulk select/deselect candidates"""
        # Verify all candidates belong to user's projects
        candidates = self.db.query(Candidate).join(Project).filter(
            Candidate.id.in_(candidate_ids),
            Project.owner_id == owner_id
        ).all()
        
        if len(candidates) != len(candidate_ids):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Some candidates not found or access denied"
            )

        # Update selection status
        for candidate in candidates:
            candidate.selected = selected

        self.db.commit()
        
        return {
            "success": True,
            "updated_count": len(candidates),
            "selected": selected
        }

    async def score_all_candidates(self, project_id: int, owner_id: int) -> Dict[str, Any]:
        """Score all candidates in a project"""
        # Verify project ownership
        project = self.db.query(Project).filter(
            Project.id == project_id,
            Project.owner_id == owner_id
        ).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        # Get unscored candidates (those with parsing completed but not yet scored)
        candidates = self.db.query(Candidate).filter(
            Candidate.project_id == project_id,
            Candidate.processing_status.in_([CandidateStatus.PARSING_COMPLETED, CandidateStatus.COMPLETED]),
            Candidate.scored_at.is_(None)
        ).all()

        scored_count = 0
        for candidate in candidates:
            try:
                await self._score_candidate(candidate)
                scored_count += 1
            except Exception as e:
                print(f"Error scoring candidate {candidate.id}: {str(e)}")

        return {
            "success": True,
            "scored_count": scored_count,
            "total_candidates": len(candidates)
        }

    def export_selected_candidates(self, project_id: int, owner_id: int) -> Dict[str, Any]:
        """Export selected candidates for contact"""
        # Verify project ownership
        project = self.db.query(Project).filter(
            Project.id == project_id,
            Project.owner_id == owner_id
        ).first()

        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        # Get selected candidates
        selected_candidates = self.db.query(Candidate).filter(
            Candidate.project_id == project_id,
            Candidate.selected == True
        ).all()

        # Format export data
        candidates_data = []
        for candidate in selected_candidates:
            personal_info = candidate.parsed_data.get("personal_info", {}) if candidate.parsed_data else {}

            # Extract key strengths from scoring
            key_strengths = []
            if candidate.scoring_results and candidate.scoring_results.get("scores"):
                scores = candidate.scoring_results["scores"]
                for dimension, data in scores.items():
                    if data.get("score", 0) >= 80:  # High scores
                        key_strengths.append(dimension.replace("_", " ").title())

            candidates_data.append({
                "name": personal_info.get("name"),
                "email": personal_info.get("email"),
                "phone": personal_info.get("phone"),
                "location": personal_info.get("location"),
                "final_score": candidate.final_score,
                "key_strengths": key_strengths,
                "selection_notes": candidate.selection_notes,
                "cv_filename": candidate.original_filename
            })

        return {
            "success": True,
            "data": {
                "project_title": project.title,
                "project_id": project_id,
                "selected_count": len(candidates_data),
                "candidates": candidates_data,
                "exported_at": datetime.utcnow().isoformat()
            }
        }
