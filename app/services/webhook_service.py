import json
import urllib.parse
import hmac
import hashlib
from typing import Dict, Any, Optional
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.config import settings
from app.models.webhook import WebhookEvent
from app.models.candidate import Candidate
from app.models.project import Project
from app.schemas.webhook import HR<PERSON>lowWebhookPayload, WebhookEventCreate
from app.services.hrflow_service import HRFlowService
from app.constants.candidate_status import CandidateStatus


class WebhookService:
    def __init__(self, db: Session):
        self.db = db
        self.hrflow_service = HRFlowService()

    def verify_webhook_signature(self, payload: str, signature: str) -> bool:
        """
        Verify webhook signature from HRFlow
        """
        if not settings.HRFLOW_WEBHOOK_SECRET:
            # If no secret is configured, skip verification
            print("No webhook secret configured, skipping signature verification")
            return True

        if not signature:
            print("No signature provided, skipping verification")
            return True
        return True
        

        expected_signature = hmac.new(
            settings.HRFLOW_WEBHOOK_SECRET.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        # Try different signature formats
        expected_formats = [
            f"sha256={expected_signature}",
            expected_signature
        ]

        for expected in expected_formats:
            if hmac.compare_digest(expected, signature):
                print("Webhook signature verified successfully")
                return True

        print(f"Webhook signature verification failed. Expected one of: {expected_formats}, got: {signature}")
        return False

    def parse_webhook_payload(self, form_data: Dict[str, str]) -> HRFlowWebhookPayload:
        """
        Parse the webhook form data into a structured payload
        """
        try:
            print(f"Parsing webhook payload: {form_data}")
            return HRFlowWebhookPayload(**form_data)
        except Exception as e:
            print(f"Webhook payload parsing error: {str(e)}")
            print(f"Form data received: {form_data}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid webhook payload: {str(e)}"
            )

    async def process_webhook(self, payload: HRFlowWebhookPayload, raw_payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the webhook payload and update the database
        """
        # Create webhook event record
        webhook_event = self.create_webhook_event(payload, raw_payload)

        try:
            if payload.type in ["profile.parsing.success", "profile.searching.success", "profile.storing.success"]:
                await self.handle_profile_success(payload)
                webhook_event.processed = True
                webhook_event.processed_at = datetime.utcnow()
            elif payload.type.startswith("profile."):
                # Handle other profile-related events
                print(f"Received profile event: {payload.type} - logging but not processing")
                webhook_event.processed = True
                webhook_event.processed_at = datetime.utcnow()
            else:
                # Log unknown webhook types but don't fail
                print(f"Unknown webhook type: {payload.type}")
                webhook_event.processing_error = f"Unknown webhook type: {payload.type}"
                webhook_event.processed = True  # Mark as processed even if unknown
                webhook_event.processed_at = datetime.utcnow()

        except Exception as e:
            print(f"Error processing webhook: {str(e)}")
            webhook_event.processing_error = str(e)
            # Don't re-raise the exception, just log it and mark as failed

        finally:
            self.db.commit()

        return {"success": True, "webhook_event_id": webhook_event.id}

    def create_webhook_event(self, payload: HRFlowWebhookPayload, raw_payload: Dict[str, Any]) -> WebhookEvent:
        """
        Create a webhook event record in the database
        """
        profile_data = payload.profile if isinstance(payload.profile, dict) else {}

        # Extract profile and source keys
        profile_key = profile_data.get("key") if profile_data else None
        source_key = None
        if profile_data and isinstance(profile_data.get("source"), dict):
            source_key = profile_data.get("source", {}).get("key")

        webhook_event = WebhookEvent(
            event_type=payload.type,
            origin=payload.origin,
            message=payload.message,
            profile_key=profile_key,
            source_key=source_key,
            raw_payload=raw_payload
        )

        self.db.add(webhook_event)
        self.db.flush()  # Get the ID without committing
        return webhook_event

    async def handle_profile_success(self, payload: HRFlowWebhookPayload):
        """
        Handle profile success webhooks (parsing, searching, or storing)
        """
        profile_data = payload.profile if isinstance(payload.profile, dict) else {}
        profile_key = profile_data.get("key")
        source_key = profile_data.get("source", {}).get("key") if isinstance(profile_data.get("source"), dict) else None

        print(f"=== WEBHOOK PROCESSING DEBUG ===")
        print(f"Webhook type: {payload.type}")
        print(f"Profile data: {profile_data}")
        print(f"Profile key: {profile_key}")
        print(f"Source key: {source_key}")

        # Handle different webhook types
        if payload.type == "profile.storing.success":
            # UI webhooks don't include profile data, just log the event
            print(f"Profile storing success webhook received (type: {payload.type})")
            print("This is likely from HRFlow UI - no profile data to process")
            return

        if not profile_key:
            print(f"No profile key found in webhook payload for type: {payload.type}")
            print(f"Profile data: {profile_data}")
            # Don't raise error, just log and return
            return

        print(f"Processing profile: {profile_key} from source: {source_key}")

        # Find candidate with this HRFlow profile ID
        print(f"Searching for candidate with hrflow_profile_id: {profile_key}")
        candidate = self.db.query(Candidate).filter(
            Candidate.hrflow_profile_id == profile_key
        ).first()

        if not candidate:
            # Try to find a candidate that's in processing status without hrflow_profile_id
            # This handles the case where async processing was used
            print(f"No candidate found with hrflow_profile_id {profile_key}")
            print("Searching for candidates in processing status without hrflow_profile_id...")

            processing_candidates = self.db.query(Candidate).filter(
                Candidate.processing_status == CandidateStatus.PROCESSING,
                Candidate.hrflow_profile_id.is_(None)
            ).order_by(Candidate.uploaded_at.desc()).all()

            print(f"Found {len(processing_candidates)} candidates in processing status")

            if processing_candidates:
                # For now, take the most recent one
                # In a production system, you might want more sophisticated matching
                candidate = processing_candidates[0]
                print(f"Using most recent processing candidate: {candidate.id} (uploaded: {candidate.uploaded_at})")

                # Set the hrflow_profile_id now that we know it
                candidate.hrflow_profile_id = profile_key

                # Update existing candidate with processed data
                await self.update_candidate_from_hrflow_profile(candidate, profile_key)
            else:
                # If no processing candidate found, create a new one
                # This might be a profile uploaded directly to HRFlow
                print(f"No processing candidates found, creating new candidate for profile {profile_key}")
                await self.create_candidate_from_hrflow_profile(profile_key)
        else:
            # Update existing candidate with processed data
            print(f"Found existing candidate {candidate.id} with profile {profile_key}")
            print(f"Current candidate status: {candidate.processing_status}")
            await self.update_candidate_from_hrflow_profile(candidate, profile_key)

    async def create_candidate_from_hrflow_profile(self, profile_key: str):
        """
        Create a new candidate from HRFlow profile data
        """
        try:
            # Fetch full profile data from HRFlow
            profile_data = await self.fetch_hrflow_profile(profile_key)

            # Extract structured data
            structured_data = self.hrflow_service.extract_structured_data({"data": profile_data})

            # We need a project_id, but we don't have one from the webhook
            # Let's create a default "Webhook Imports" project or find an existing one
            default_project = await self.get_or_create_default_project()

            # Create a new candidate record
            candidate = Candidate(
                project_id=default_project.id,
                cv_filename=f"hrflow_profile_{profile_key}.json",  # Virtual filename
                original_filename=f"HRFlow Profile {profile_key}",
                file_size=len(str(profile_data)),
                hrflow_profile_id=profile_key,
                hrflow_source_key=settings.HRFLOW_SOURCE_KEY,
                hrflow_response={"data": profile_data},
                parsed_data=structured_data,
                processing_status=CandidateStatus.PARSING_COMPLETED,
                processed_at=datetime.utcnow()
            )

            self.db.add(candidate)
            self.db.commit()

            print(f"Created new candidate {candidate.id} from HRFlow profile {profile_key}")
            return candidate

        except Exception as e:
            error_message = str(e) if str(e) else f"{type(e).__name__}: {repr(e)}"
            print(f"Error creating candidate from HRFlow profile {profile_key}: {error_message}")

            # Also log the full traceback for debugging
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

            raise Exception(f"Failed to create candidate from HRFlow profile: {error_message}")

    async def update_candidate_from_hrflow_profile(self, candidate: Candidate, profile_key: str):
        """
        Update existing candidate with fresh HRFlow profile data
        """
        try:
            # Fetch full profile data from HRFlow
            profile_data = await self.fetch_hrflow_profile(profile_key)

            # Extract structured data
            structured_data = self.hrflow_service.extract_structured_data({"data": profile_data})

            # Update candidate
            candidate.hrflow_response = {"data": profile_data}
            candidate.parsed_data = structured_data
            candidate.processing_status = CandidateStatus.PARSING_COMPLETED
            candidate.processed_at = datetime.utcnow()

            # Update source key from environment configuration
            candidate.hrflow_source_key = settings.HRFLOW_SOURCE_KEY

            # Update basic info from structured data
            if structured_data.get("name"):
                candidate.name = structured_data.get("name")
            if structured_data.get("email"):
                candidate.email = structured_data.get("email")
            if structured_data.get("phone"):
                candidate.phone = structured_data.get("phone")

            self.db.commit()
            print(f"Updated candidate {candidate.id} with fresh HRFlow data")

        except Exception as e:
            error_message = str(e) if str(e) else f"{type(e).__name__}: {repr(e)}"
            print(f"Error updating candidate {candidate.id} from HRFlow profile {profile_key}: {error_message}")

            # Also log the full traceback for debugging
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

            raise Exception(f"Failed to update candidate from HRFlow profile: {error_message}")

    async def fetch_hrflow_profile(self, profile_key: str) -> Dict[str, Any]:
        """
        Fetch full profile data from HRFlow API using SDK
        """
        if not settings.HRFLOW_API_KEY:
            raise ValueError("HRFlow API key not configured")

        # Get source key from environment configuration
        if not settings.HRFLOW_SOURCE_KEY:
            raise ValueError("HRFlow source key not configured")

        try:
            print(f"Fetching HRFlow profile {profile_key} from source {settings.HRFLOW_SOURCE_KEY}")

            # Use HRFlow SDK to get profile
            # Based on HRFlow SDK v3.0.0 documentation
            response = self.hrflow_service.client.profile.storing.get(
                source_key=settings.HRFLOW_SOURCE_KEY,
                key=profile_key
            )

            if not response or not response.get("data"):
                raise ValueError("No profile data returned from HRFlow")

            print(f"Successfully fetched profile data for {profile_key}")
            return response["data"]

        except Exception as e:
            error_message = str(e) if str(e) else f"{type(e).__name__}: {repr(e)}"
            print(f"HRFlow SDK error: {error_message}")

            # Also log the full traceback for debugging
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"Failed to fetch HRFlow profile: {error_message}"
            )

    async def get_or_create_default_project(self) -> Project:
        """
        Get or create a default project for webhook-imported candidates
        """
        project_name = "HRFlow Webhook Imports"
        project_description = "Candidates imported automatically via HRFlow webhooks"

        # Try to find existing default project
        project = self.db.query(Project).filter(
            Project.name == project_name
        ).first()

        if not project:
            # Create new default project
            project = Project(
                name=project_name,
                description=project_description,
                job_description="Candidates imported from HRFlow webhooks - no specific job description",
                status="active"
            )
            self.db.add(project)
            self.db.flush()  # Get the ID without committing
            print(f"Created default project '{project_name}' with ID {project.id}")

        return project
