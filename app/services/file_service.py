import os
import uuid
import magic
from typing import List, Tu<PERSON>, Dict
from fastapi import HTTPException, status, UploadFile

from app.core.config import settings


class FileService:
    def __init__(self):
        self.upload_dir = settings.UPLOAD_DIR
        self.max_file_size = settings.max_file_size_bytes
        self.allowed_extensions = settings.allowed_file_extensions
        self.max_total_size = self.max_file_size * 10  # Allow up to 10x single file limit for batch
        
        # Create upload directory if it doesn't exist
        os.makedirs(self.upload_dir, exist_ok=True)

    def validate_batch_size(self, files: List[UploadFile]) -> None:
        """
        Validate the total size of all files in a batch
        """
        total_size = 0
        for file in files:
            if file.size:
                total_size += file.size
        
        if total_size > self.max_total_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"Total batch size {total_size} bytes exceeds maximum allowed batch size"
            )

    def validate_file(self, file: UploadFile) -> None:
        """
        Validate uploaded file with enhanced security checks
        """
        # Check if filename exists
        if not file.filename or file.filename.strip() == "":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must have a valid filename"
            )
        
        # Sanitize filename to prevent path traversal attacks
        filename = os.path.basename(file.filename.strip())
        if filename != file.filename.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid filename format"
            )
        
        # Check for hidden files or files starting with dots
        if filename.startswith('.'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Hidden files are not allowed"
            )
        
        # Check if file has extension
        if '.' not in filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must have an extension"
            )
        
        # Check file extension
        file_extension = filename.split('.')[-1].lower()
        if not file_extension:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must have a valid extension"
            )
            
        if file_extension not in self.allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Allowed types: {', '.join(self.allowed_extensions)}"
            )
        
        # Initial file size check (if available)
        if file.size and file.size > self.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE_MB}MB"
            )

    def validate_file_content(self, file_content: bytes, filename: str) -> None:
        """
        Validate file content using python-magic with enhanced checks
        """
        # Check if file content is empty
        if not file_content or len(file_content) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File content is empty"
            )
        
        # Check file size after reading
        if len(file_content) > self.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size {len(file_content)} bytes exceeds maximum allowed size of {settings.MAX_FILE_SIZE_MB}MB"
            )
        
        # Check for minimum file size (avoid obviously corrupted files)
        if len(file_content) < 100:  # 100 bytes minimum for a valid document
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File appears to be corrupted or too small"
            )
        
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            
            # Define allowed MIME types
            allowed_mime_types = {
                'pdf': ['application/pdf'],
                'doc': ['application/msword'],
                'docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'application/zip']  # docx files might be detected as zip
            }
            
            file_extension = filename.split('.')[-1].lower()
            expected_mimes = allowed_mime_types.get(file_extension, [])
            
            if expected_mimes and mime_type not in expected_mimes:
                # More lenient check for docx files
                if file_extension == 'docx' and 'zip' in mime_type:
                    # Additional check for docx structure
                    if not self._validate_docx_structure(file_content):
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"File content doesn't match docx format"
                        )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"File content doesn't match extension. Expected one of {expected_mimes}, got {mime_type}"
                    )
                    
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            # If magic fails, we'll allow the file but log the error
            print(f"File validation warning for {filename}: {str(e)}")

    def _validate_docx_structure(self, file_content: bytes) -> bool:
        """
        Basic validation for docx file structure
        """
        try:
            # Check for ZIP signature (docx is a ZIP file)
            zip_signature = b'PK\x03\x04'
            if not file_content.startswith(zip_signature):
                return False
            
            # Look for typical docx content
            docx_indicators = [b'word/', b'[Content_Types].xml', b'document.xml']
            return any(indicator in file_content for indicator in docx_indicators)
        except Exception:
            return False

    async def save_temporary_file(self, file: UploadFile) -> Tuple[str, bytes]:
        """
        Save file temporarily and return file path and content with enhanced validation
        """
        # Validate file metadata first
        self.validate_file(file)
        
        # Sanitize filename
        sanitized_filename = os.path.basename(file.filename.strip())
        
        try:
            # Read file content
            file_content = await file.read()
            
            # Validate file content
            self.validate_file_content(file_content, sanitized_filename)
            
            # Generate unique filename to avoid conflicts
            file_extension = sanitized_filename.split('.')[-1].lower()
            unique_filename = f"{uuid.uuid4()}.{file_extension}"
            file_path = os.path.join(self.upload_dir, unique_filename)
            
            # Ensure upload directory exists
            os.makedirs(self.upload_dir, exist_ok=True)
            
            # Save file temporarily with proper error handling
            try:
                with open(file_path, "wb") as buffer:
                    buffer.write(file_content)
            except OSError as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to save file to disk: {str(e)}"
                )
            
            # Verify file was written correctly
            if not os.path.exists(file_path):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="File was not saved correctly"
                )
            
            # Verify file size matches
            saved_size = os.path.getsize(file_path)
            if saved_size != len(file_content):
                # Cleanup the incomplete file
                self.cleanup_file(file_path)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="File was not saved completely"
                )
            
            return file_path, file_content
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error processing file: {str(e)}"
            )

    def cleanup_file(self, file_path: str) -> None:
        """
        Remove temporary file with enhanced safety checks
        """
        if not file_path:
            return
            
        try:
            # Security check: ensure file is within upload directory
            abs_file_path = os.path.abspath(file_path)
            abs_upload_dir = os.path.abspath(self.upload_dir)
            
            if not abs_file_path.startswith(abs_upload_dir):
                print(f"Security warning: Attempted to delete file outside upload directory: {file_path}")
                return
            
            if os.path.exists(abs_file_path) and os.path.isfile(abs_file_path):
                os.remove(abs_file_path)
                print(f"Successfully cleaned up file: {file_path}")
            else:
                print(f"File not found for cleanup: {file_path}")
                
        except Exception as e:
            print(f"Error cleaning up file {file_path}: {str(e)}")

    def cleanup_multiple_files(self, file_paths: List[str]) -> Dict[str, int]:
        """
        Cleanup multiple files and return statistics
        """
        successful_cleanups = 0
        failed_cleanups = 0
        
        for file_path in file_paths:
            try:
                self.cleanup_file(file_path)
                successful_cleanups += 1
            except Exception:
                failed_cleanups += 1
        
        return {
            "successful_cleanups": successful_cleanups,
            "failed_cleanups": failed_cleanups,
            "total_files": len(file_paths)
        }

    async def process_multiple_files(self, files: List[UploadFile]) -> List[Tuple[str, str, bytes]]:
        """
        Process multiple files and return list of (original_filename, temp_path, content)
        with enhanced error handling and validation
        """
        if not files:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No files provided for processing"
            )
            
        if len(files) > 20:  # Reasonable limit
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Too many files. Maximum 20 files allowed per upload."
            )
        
        # Validate total batch size
        self.validate_batch_size(files)
        
        processed_files = []
        failed_files = []
        temp_files_for_cleanup = []
        
        # Track filenames to detect duplicates
        processed_filenames = set()
        
        for i, file in enumerate(files):
            try:
                # Check for duplicate filenames
                if file.filename in processed_filenames:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Duplicate filename detected: {file.filename}"
                    )
                
                # Reset file position if needed (some frameworks read files multiple times)
                if hasattr(file.file, 'seek'):
                    file.file.seek(0)
                
                temp_path, content = await self.save_temporary_file(file)
                temp_files_for_cleanup.append(temp_path)
                
                processed_files.append((file.filename, temp_path, content))
                processed_filenames.add(file.filename)
                
            except HTTPException as e:
                failed_files.append({
                    "filename": file.filename if file.filename else f"file_{i}",
                    "error": e.detail,
                    "status_code": e.status_code
                })
                
                # If we have specific file errors, cleanup and re-raise with context
                if len(failed_files) == 1 and len(files) == 1:
                    # Cleanup any successfully processed files
                    for temp_path in temp_files_for_cleanup:
                        self.cleanup_file(temp_path)
                    raise e
                    
            except Exception as e:
                failed_files.append({
                    "filename": file.filename if file.filename else f"file_{i}",
                    "error": str(e),
                    "status_code": 500
                })
        
        # If we have any failures in batch processing, cleanup and report
        if failed_files:
            # Cleanup any successfully processed files
            for temp_path in temp_files_for_cleanup:
                self.cleanup_file(temp_path)
                
            # Create detailed error message
            error_details = []
            for failed_file in failed_files:
                error_details.append(f"{failed_file['filename']}: {failed_file['error']}")
            
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File processing failed for {len(failed_files)} file(s): {'; '.join(error_details)}"
            )
        
        return processed_files

    def get_file_info(self, file_path: str) -> Dict[str, any]:
        """
        Get information about a saved file
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            stat = os.stat(file_path)
            
            return {
                "path": file_path,
                "size": stat.st_size,
                "created": stat.st_ctime,
                "modified": stat.st_mtime,
                "exists": True
            }
        except Exception as e:
            return {
                "path": file_path,
                "error": str(e),
                "exists": False
            }
