from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import Dict, Any
import urllib.parse
import qs_codec as qs

from app.core.database import get_db
from app.services.webhook_service import WebhookService
from app.schemas.webhook import HRFlowWebhookPayload

router = APIRouter()


@router.post("/hrflow")
async def hrflow_webhook(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    HRFlow webhook endpoint for profile parsing updates

    Receives webhook notifications when profiles are processed by HRFlow
    """
    webhook_service = WebhookService(db)

    # Log the incoming request for debugging
    print(f"Webhook received - Headers: {dict(request.headers)}")

    # Get raw body to see what we're actually receiving
    body = await request.body()
    # get substring to avoid memory error
    print(f"Raw body: {body.decode()[:100]}")

    # Try to parse form data
    try:
        print("Attempting to parse form data...")
        form = await request.form()
        print(f"Form parsed successfully, type: {type(form)}")
        form_data = dict(form)
        print(f"Form data converted to dict, keys: {list(form_data.keys())}")
        # limit form data to 100 characters for display
        form_data_str = str(form_data)
        print(f"Parsed form data: {form_data_str[:100]}...")
    except Exception as e:
        print(f"Error parsing form data: {e}")
        print(f"Error type: {type(e)}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Could not parse form data: {str(e)}"
        )

    # Use qs_codec to parse nested form data
    parsed_data = qs.decode(body.decode())
    
    # Extract fields with flexible handling for different webhook formats
    webhook_type = parsed_data.get("type", "")
    origin = parsed_data.get("origin", "hrflow_ui")  # Default for UI webhooks
    message = parsed_data.get("message", f"HRFlow webhook: {webhook_type}")  # Default message
    team_name = parsed_data.get("team_name", "")  # Additional field from UI webhooks

    # Handle profile data - qs_codec automatically parses nested form data
    profile = parsed_data.get("profile", {})  # qs_codec converts nested keys to objects

    # If profile is still a string, try to parse as JSON
    if isinstance(profile, str):
        try:
            import json
            profile = json.loads(profile) if profile != "{}" else {}
        except json.JSONDecodeError:
            profile = {}

    print(f"Extracted profile using qs_codec: {profile}")

    # Validate that we at least have a type
    if not webhook_type:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing required field: type"
        )

    print(f"Processing webhook: type={webhook_type}, origin={origin}, team_name={team_name}")

    # Verify webhook signature (if configured)
    signature = request.headers.get("X-Signature", "") or request.headers.get("http-hrflow-signature", "")
    if not webhook_service.verify_webhook_signature(body.decode(), signature):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid webhook signature"
        )

    # Create structured payload
    structured_form_data = {
        "type": webhook_type,
        "origin": origin,
        "message": message,
        "profile": profile
    }

    try:
        # Parse the webhook payload
        payload = webhook_service.parse_webhook_payload(structured_form_data)

        # Process the webhook
        result = await webhook_service.process_webhook(payload, structured_form_data)

        return result

    except Exception as e:
        print(f"Webhook processing error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Webhook processing failed: {str(e)}"
        )


@router.post("/debug")
async def debug_webhook(request: Request):
    # use qs 
    body = await request.body()
    parsed = qs.decode(body.decode())
    return parsed


@router.get("/test")
async def test_webhook():
    """
    Test endpoint to verify webhook configuration
    """
    return {
        "message": "Webhook endpoint is active",
        "timestamp": "2024-08-06T00:00:00Z"
    }
