from sqlalchemy import Column, Integer, String, DateTime, JSON, Text, Boolean
from sqlalchemy.sql import func

from app.core.database import Base


class WebhookEvent(Base):
    __tablename__ = "webhook_events"

    id = Column(Integer, primary_key=True, index=True)
    
    # Event details
    event_type = Column(String, nullable=False)  # e.g., "profile.parsing.success"
    origin = Column(String, nullable=False)  # e.g., "api"
    message = Column(Text)
    
    # Profile information from HRFlow
    profile_key = Column(String)  # HRFlow profile key
    source_key = Column(String)  # HRFlow source key
    
    # Raw webhook data
    raw_payload = Column(JSON)  # Complete webhook payload
    
    # Processing status
    processed = Column(Boolean, default=False)
    processing_error = Column(Text)
    
    # Metadata
    received_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True))
