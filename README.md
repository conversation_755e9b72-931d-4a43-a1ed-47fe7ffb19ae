# CV Scoring API

A comprehensive project-based CV scoring platform that allows recruiters to create hiring projects, upload candidate CVs, provide job descriptions, and receive AI-powered candidate rankings.

## Features

- **Project-Based Workflow**: Organize hiring activities into discrete projects
- **CV Processing**: Upload and parse CVs using HRFlow API integration
- **AI-Powered Scoring**: Multi-dimensional candidate assessment using OpenAI
- **Webhook Integration**: Real-time CV processing updates via HRFlow webhooks
- **Security First**: Prompt injection prevention and input sanitization
- **Candidate Selection**: Select and export top candidates for contact
- **RESTful API**: Complete API with automatic documentation

## Quick Start

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd cv-scoring-api

# Copy environment configuration
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 2. Database Setup

#### Option A: Using Docker (Recommended)

```bash
# Start services
docker-compose up -d

# Initialize database
docker-compose exec api python scripts/init_db.py
```

#### Option B: Local Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Set up PostgreSQL database
createdb cv_scoring_db

# Initialize database
python scripts/init_db.py
```

### 3. Run the Application

```bash
# Using Docker
docker-compose up

# Or locally
uvicorn app.main:app --reload
```

### 4. Access the API

- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## Configuration

### Required Environment Variables

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/cv_scoring_db

# JWT Authentication
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# HRFlow API
HRFLOW_API_KEY=your-hrflow-api-key

# OpenAI API
OPENAI_API_KEY=your-openai-api-key

# HRFlow Webhook Integration
HRFLOW_API_KEY=your-hrflow-api-key
HRFLOW_SOURCE_KEY=your-hrflow-source-key
HRFLOW_WEBHOOK_SECRET=your-webhook-secret-key

# Redis (for background tasks)
REDIS_URL=redis://localhost:6379/0
```

## API Usage

### 1. Authentication

```bash
# Create user account
curl -X POST "http://localhost:8000/api/v1/users/" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "full_name": "John Doe"
  }'

# Login to get access token
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=password123"
```

### 2. Create a Project

```bash
curl -X POST "http://localhost:8000/api/v1/projects/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Senior Software Engineer",
    "description": "Backend development role",
    "job_title": "Senior Software Engineer",
    "company": "TechCorp",
    "job_description": "We are looking for a senior software engineer...",
    "job_requirements": {
      "required_skills": ["Python", "FastAPI", "PostgreSQL"],
      "min_experience_years": 5,
      "required_education": "Bachelor degree in Computer Science"
    }
  }'
```

### 3. Upload CVs

```bash
curl -X POST "http://localhost:8000/api/v1/projects/1/candidates" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files=@cv1.pdf" \
  -F "files=@cv2.pdf" \
  -F "auto_score=true"
```

### 4. View Candidates

```bash
curl -X GET "http://localhost:8000/api/v1/projects/1/candidates?sort_by=final_score&order=desc" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. Select Candidates

```bash
curl -X PUT "http://localhost:8000/api/v1/candidates/1/select" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "selected": true,
    "selection_notes": "Strong technical background"
  }'
```

### 6. Export Selected Candidates

```bash
curl -X GET "http://localhost:8000/api/v1/projects/1/selected-candidates" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## HRFlow Webhook Integration

### Overview

This application now supports HRFlow webhooks for real-time CV processing updates. When a CV is uploaded, it's sent to HRFlow for asynchronous processing, and your application receives automatic updates via webhooks when processing completes.

### Webhook Configuration

1. **Set Environment Variables**:
```env
HRFLOW_WEBHOOK_SECRET=your-secret-key-for-webhook-verification
```

2. **Configure HRFlow Webhook URL**:
In your HRFlow dashboard, set the webhook URL to:
```
https://your-domain.com/api/v1/webhooks/hrflow
```

### Webhook Workflow

1. CV uploaded to your application
2. Application sends CV to HRFlow for processing (returns 202 status)
3. HRFlow processes CV asynchronously
4. HRFlow sends webhook notification when complete
5. Application automatically updates candidate record with parsed data

### Testing Webhooks

Use the provided test script:
```bash
python test_webhook.py
```

For detailed webhook integration information, see `WEBHOOK_INTEGRATION.md`.

## Architecture

### Core Components

- **FastAPI**: Web framework and API server
- **SQLAlchemy**: Database ORM and migrations
- **PostgreSQL**: Primary database
- **Redis**: Caching and background tasks
- **HRFlow API**: CV parsing service
- **OpenAI API**: AI-powered scoring

### Security Features

- JWT-based authentication
- Input validation and sanitization
- Prompt injection detection
- Rate limiting
- File type validation
- CORS protection

### Scoring Dimensions

1. **Education Relevance** (25%): Education match with job requirements
2. **Skills Match** (25%): Alignment of candidate skills with job needs
3. **Experience Quality** (20%): Relevance and quality of work experience
4. **Technical Proficiency** (15%): Technical skills assessment
5. **Career Progression** (10%): Career growth pattern
6. **Language Fit** (5%): Language requirements match

## Development

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest
```

### Code Quality

```bash
# Format code
black app/

# Sort imports
isort app/

# Type checking
mypy app/
```

### Database Migrations

```bash
# Create migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## Deployment

### Production Considerations

1. **Environment Variables**: Use secure secret management
2. **Database**: Use managed PostgreSQL service
3. **File Storage**: Use cloud storage for CV files
4. **Monitoring**: Implement logging and monitoring
5. **Scaling**: Use load balancers and multiple instances

### Docker Production

```bash
# Build production image
docker build -t cv-scoring-api:latest .

# Run with production settings
docker run -d \
  --name cv-scoring-api \
  -p 8000:8000 \
  --env-file .env.production \
  cv-scoring-api:latest
```

## Troubleshooting

### Common Issues

1. **Database Connection**: Check DATABASE_URL and PostgreSQL service
2. **HRFlow API**: Verify API key and network connectivity
3. **OpenAI API**: Check API key and rate limits
4. **File Uploads**: Ensure upload directory permissions
5. **Memory Usage**: Monitor for large file processing

### Logs

```bash
# View application logs
docker-compose logs api

# View database logs
docker-compose logs postgres
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/docs`
- Review the troubleshooting section
