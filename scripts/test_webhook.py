#!/usr/bin/env python3
"""
Test script for HRFlow webhook integration
"""

import asyncio
import httpx
import json
from urllib.parse import urlencode


async def test_webhook_endpoint():
    """Test the webhook endpoint with a sample payload"""
    
    # Sample webhook payload (as it would come from HRFlow)
    webhook_data = {
        "type": "profile.parsing.success",
        "origin": "api", 
        "message": "profile parsing succeed",
        "profile": json.dumps({
            "key": "d821393853fc32b08c93b8d38590817c72048ec4",
            "source": {"key": "d900ec70c67d43c71027f9bc63ec3b5b3e16c1d8"}
        })
    }
    
    # URL encode the data as HR<PERSON>low sends it
    form_data = urlencode(webhook_data)
    
    # Test the webhook endpoint
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "http://localhost:8000/api/v1/webhooks/hrflow",
                content=form_data,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "X-Signature": "sha256=test_signature"  # In real usage, this would be calculated
                }
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.json()}")
            
        except Exception as e:
            print(f"Error testing webhook: {e}")


async def test_hrflow_cv_upload():
    """Test CV upload to HRFlow with webhook enabled"""
    
    # This would typically be done through your API
    test_cv_content = b"Sample CV content"  # In real usage, this would be actual file content
    
    async with httpx.AsyncClient() as client:
        try:
            # Test the CV upload endpoint (you'd need to implement this in your API)
            files = {"file": ("test_cv.pdf", test_cv_content, "application/pdf")}
            data = {"project_id": 1}
            
            response = await client.post(
                "http://localhost:8000/api/v1/projects/1/candidates/upload",
                files=files,
                data=data,
                headers={"Authorization": "Bearer your_jwt_token"}
            )
            
            print(f"Upload Status: {response.status_code}")
            print(f"Upload Response: {response.json()}")
            
        except Exception as e:
            print(f"Error testing CV upload: {e}")


async def test_webhook_connectivity():
    """Test webhook endpoint connectivity"""
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8000/api/v1/webhooks/test")
            print(f"Webhook test endpoint: {response.status_code}")
            print(f"Response: {response.json()}")
            
        except Exception as e:
            print(f"Error testing webhook connectivity: {e}")


if __name__ == "__main__":
    print("Testing HRFlow Webhook Integration...")
    print("=" * 50)
    
    # Test webhook connectivity
    print("\n1. Testing webhook connectivity...")
    asyncio.run(test_webhook_connectivity())
    
    # Test webhook endpoint with sample data
    print("\n2. Testing webhook endpoint...")
    asyncio.run(test_webhook_endpoint())
    
    # Test CV upload (optional - requires authentication)
    print("\n3. Testing CV upload (requires running API server)...")
    # asyncio.run(test_hrflow_cv_upload())
    
    print("\nTest completed!")
