"""Add webhook support and update candidate model

Revision ID: add_webhook_support
Revises: 
Create Date: 2025-08-06 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_webhook_support'
down_revision = None
depends_on = None


def upgrade():
    # Create webhook_events table
    op.create_table(
        'webhook_events',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('event_type', sa.String(), nullable=False),
        sa.Column('origin', sa.String(), nullable=False),
        sa.Column('message', sa.Text(), nullable=True),
        sa.Column('profile_key', sa.String(), nullable=True),
        sa.Column('source_key', sa.String(), nullable=True),
        sa.Column('raw_payload', sa.JSON(), nullable=True),
        sa.Column('processed', sa.<PERSON>(), nullable=True, default=False),
        sa.Column('processing_error', sa.Text(), nullable=True),
        sa.Column('received_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_webhook_events_id'), 'webhook_events', ['id'], unique=False)
    op.create_index(op.f('ix_webhook_events_profile_key'), 'webhook_events', ['profile_key'], unique=False)
    op.create_index(op.f('ix_webhook_events_event_type'), 'webhook_events', ['event_type'], unique=False)
    
    # Add hrflow_source_key column to candidates table if it doesn't exist
    # Note: This assumes the candidates table already exists
    try:
        op.add_column('candidates', sa.Column('hrflow_source_key', sa.String(), nullable=True))
    except Exception:
        # Column might already exist, skip
        pass


def downgrade():
    # Remove the hrflow_source_key column from candidates
    try:
        op.drop_column('candidates', 'hrflow_source_key')
    except Exception:
        pass
    
    # Drop webhook_events table
    op.drop_index(op.f('ix_webhook_events_event_type'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_profile_key'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_id'), table_name='webhook_events')
    op.drop_table('webhook_events')
