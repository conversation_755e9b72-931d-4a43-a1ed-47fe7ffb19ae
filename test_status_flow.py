#!/usr/bin/env python3
"""
Test script to verify the candidate status flow works correctly
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from constants.candidate_status import CandidateStatus

def test_candidate_status_constants():
    """Test that all status constants are defined correctly"""
    print("Testing CandidateStatus constants...")
    
    # Test all status values
    assert CandidateStatus.PENDING == "pending"
    assert CandidateStatus.PROCESSING == "processing"
    assert CandidateStatus.PARSING_COMPLETED == "parsing_completed"
    assert CandidateStatus.COMPLETED == "completed"
    assert CandidateStatus.FAILED == "failed"
    
    print("✓ All status constants are correct")
    
    # Test helper methods
    all_statuses = CandidateStatus.all_statuses()
    expected_statuses = ["pending", "processing", "parsing_completed", "completed", "failed"]
    assert all_statuses == expected_statuses
    print("✓ all_statuses() method works correctly")
    
    # Test validation
    assert CandidateStatus.is_valid_status("parsing_completed") == True
    assert CandidateStatus.is_valid_status("invalid_status") == False
    print("✓ is_valid_status() method works correctly")
    
    # Test processing complete check
    assert CandidateStatus.is_processing_complete("parsing_completed") == True
    assert CandidateStatus.is_processing_complete("completed") == True
    assert CandidateStatus.is_processing_complete("processing") == False
    print("✓ is_processing_complete() method works correctly")
    
    # Test final status check
    assert CandidateStatus.is_final_status("completed") == True
    assert CandidateStatus.is_final_status("failed") == True
    assert CandidateStatus.is_final_status("parsing_completed") == False
    print("✓ is_final_status() method works correctly")

def test_status_flow():
    """Test the expected status flow"""
    print("\nTesting status flow...")
    
    # Expected flow for sync processing with auto-scoring
    sync_flow = [
        CandidateStatus.PENDING,
        CandidateStatus.PROCESSING,
        CandidateStatus.PARSING_COMPLETED,
        CandidateStatus.COMPLETED
    ]
    
    # Expected flow for sync processing without auto-scoring
    sync_no_score_flow = [
        CandidateStatus.PENDING,
        CandidateStatus.PROCESSING,
        CandidateStatus.PARSING_COMPLETED
    ]
    
    # Expected flow for async processing with webhook
    async_flow = [
        CandidateStatus.PENDING,
        CandidateStatus.PROCESSING,
        CandidateStatus.PARSING_COMPLETED,  # Set by webhook
        CandidateStatus.COMPLETED  # Set by scoring if enabled
    ]
    
    print("✓ Status flows defined correctly")
    print(f"  Sync with scoring: {' -> '.join(sync_flow)}")
    print(f"  Sync without scoring: {' -> '.join(sync_no_score_flow)}")
    print(f"  Async with webhook: {' -> '.join(async_flow)}")

if __name__ == "__main__":
    print("Testing candidate status implementation...")
    print("=" * 50)
    
    try:
        test_candidate_status_constants()
        test_status_flow()
        
        print("\n" + "=" * 50)
        print("✅ All tests passed! The status implementation is working correctly.")
        print("\nKey improvements:")
        print("- Added 'parsing_completed' status when parsed_data is filled")
        print("- Status changes from 'parsing_completed' to 'completed' after scoring")
        print("- Constants defined for maintainable code")
        print("- Helper methods for status validation and checking")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
