with open('test.txt', 'r') as file:
    test_txt = file.read()

test_txt

import requests

url = "http://localhost:8000/api/v1/webhooks/debug"
headers = {"Content-Type": "application/x-www-form-urlencoded"}
data = test_txt

response = requests.post(url, headers=headers, data=data)
print(response.status_code)
print(response.text)

print(nested.get("profile"))

pip install qs-codec





import qs_codec  as qs
nested_dict = qs.decode(test_txt)

print(nested_dict)

nested_dict.get("profile")

